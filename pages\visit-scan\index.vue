<template>
  <view class="container">
    <!-- 扫描区域 -->
    <view class="scan-box">
      <!-- html5-qrcode 扫描区域 -->
      <div ref="qrReader" id="qr-reader-visit" class="qr-reader"></div>

      <!-- 扫描框和边框 -->
      <view class="scan-line"></view>
      <view class="scan-border"></view>

      <!-- 提示文本 -->
      <view class="tip-text">
        <view>将条形码放入框内进行扫描</view>
        <view class="barcode-tip">
          <span class="mode-restriction">走访专用条形码扫描</span>
        </view>
      </view>
    </view>

    <!-- 成功效果 -->
    <view v-if="showSuccessEffect" class="success-effect">
      <view class="success-icon">
        <uni-icons type="checkmarkempty" size="60" color="#fff"></uni-icons>
      </view>
      <view class="success-text">扫码成功</view>
    </view>

    <!-- 切换摄像头按钮 -->
    <view class="camera-toggle-btn" @click="toggleCamera">
      <text>切换摄像头</text>
    </view>

    <!-- 闪光灯按钮 -->
    <view class="flashlight-btn" @click="toggleFlashlight">
      <uni-icons
        :type="flashlightOn ? 'fire-filled' : 'fire'"
        size="24"
        color="#fff"
      ></uni-icons>
      <text>{{ flashlightOn ? "关闭闪光灯" : "开启闪光灯" }}</text>
    </view>

    <!-- 手动输入按钮 -->
    <view class="manual-input-btn" @click="showManualInput">
      <uni-icons type="keyboard" size="20" color="#fff"></uni-icons>
      <text>手动输入</text>
    </view>

    <!-- 扫码帮助按钮 -->
    <view class="scan-help-btn" @click="showScanTips">
      <uni-icons type="help" size="20" color="#fff"></uni-icons>
      <text>扫码帮助</text>
    </view>

    <!-- 手动输入弹窗 -->
    <uni-popup ref="inputPopup" type="center" :mask-click="false">
      <view class="input-popup">
        <view class="popup-header">
          <text class="popup-title">手动输入条形码</text>
        </view>
        <view class="popup-content">
          <input
            v-model="manualCode"
            class="manual-input"
            placeholder="请输入条形码"
            type="text"
            maxlength="50"
            @input="onManualInput"
          />
          <text class="input-tip"
            >请输入有效的条形码（数字或字母数字组合）</text
          >
        </view>
        <view class="popup-actions">
          <button class="cancel-btn" @click="closeManualInput">取消</button>
          <button
            class="confirm-btn"
            @click="confirmManualInput"
            :disabled="!isValidManualCode"
          >
            确认
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 扫码帮助弹窗 -->
    <uni-popup ref="tipsPopup" type="center" :mask-click="true">
      <view class="tips-popup">
        <view class="popup-header">
          <text class="popup-title">扫码使用帮助</text>
        </view>
        <view class="tips-content">
          <view class="tip-item">
            <text class="tip-title">• 条形码要求</text>
            <text class="tip-desc"
              >支持EAN-13、EAN-8、UPC-A、Code 128等常见条形码格式</text
            >
          </view>
          <view class="tip-item">
            <text class="tip-title">• 扫描技巧</text>
            <text class="tip-desc">保持条形码水平，距离适中，光线充足</text>
          </view>
          <view class="tip-item">
            <text class="tip-title">• 备选方案</text>
            <text class="tip-desc">如扫描失败，可使用"手动输入"功能</text>
          </view>
        </view>
        <view class="popup-actions">
          <button class="confirm-btn full-width" @click="closeTips">
            知道了
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import {
  Html5QrcodeScanner,
  Html5Qrcode,
  Html5QrcodeSupportedFormats,
} from "html5-qrcode";
import { getQrcode } from "@/api";

export default {
  data() {
    return {
      html5QrcodeScanner: null, // html5-qrcode 扫描器实例
      isScanning: false,
      showSuccessEffect: false,
      lastScanTime: 0,
      isProcessing: false,
      isDestroyed: false,
      scanType: "1", // 走访类型
      flashlightOn: false,
      currentCameraId: null, // 当前使用的摄像头ID
      availableCameras: [], // 可用摄像头列表
      scanMode: "barcode", // 固定为条形码模式
      manualCode: "",
      isValidManualCode: false,
    };
  },

  mounted() {
    console.log("[VisitScan] mounted");
    this.isScanning = false;
    this.isProcessing = false;
    this.isDestroyed = false;

    // 延迟初始化，确保DOM完全渲染
    setTimeout(() => {
      this.initScanner();
    }, 500);
  },

  onLoad() {
    console.log("[VisitScan] 页面加载");
  },

  onReady() {
    console.log("[VisitScan] 页面准备完成");
  },

  onShow() {
    console.log("[VisitScan] 页面显示");

    if (this.isDestroyed) {
      this.isDestroyed = false;
      this.isScanning = false;
      this.isProcessing = false;
    }

    // 如果扫描器未运行且未在处理中，重新初始化
    if (!this.isScanning && !this.isDestroyed && !this.isProcessing) {
      console.log("[VisitScan] 重新初始化扫描器");
      setTimeout(() => {
        this.initScanner();
      }, 1000);
    }
  },

  onHide() {
    console.log("[VisitScan] 页面隐藏");
    this.stopScanner();
  },

  onUnload() {
    console.log("[VisitScan] 页面卸载");
    this.isDestroyed = true;
    this.stopScanner();
    this.isScanning = false;
    this.isProcessing = false;
  },

  methods: {
    // 初始化扫描器
    async initScanner() {
      console.log("[VisitScan] initScanner called");

      if (this.isScanning || this.isDestroyed || this.isProcessing) {
        console.log(
          "[VisitScan] 扫描器已初始化或组件已销毁或正在处理中，取消初始化"
        );
        return false;
      }

      try {
        this.isProcessing = true;

        // 停止之前的扫描器
        await this.stopScanner();

        // 获取可用摄像头列表
        await this.getCameraList();

        // html5-qrcode 配置 - 专门针对条形码优化
        const config = {
          fps: 10, // 扫描帧率
          qrbox: { width: 300, height: 150 }, // 条形码扫描框（宽度大于高度）
          aspectRatio: 2.0, // 条形码的宽高比
          // 优先使用后置摄像头的配置
          videoConstraints: {
            facingMode: "environment", // 优先后置摄像头
          },
          // 条形码扫描格式配置
          formatsToSupport: [
            Html5QrcodeSupportedFormats.EAN_13,
            Html5QrcodeSupportedFormats.EAN_8,
            Html5QrcodeSupportedFormats.UPC_A,
            Html5QrcodeSupportedFormats.UPC_E,
            Html5QrcodeSupportedFormats.CODE_128,
            Html5QrcodeSupportedFormats.CODE_39,
            Html5QrcodeSupportedFormats.CODE_93,
            Html5QrcodeSupportedFormats.CODABAR,
            Html5QrcodeSupportedFormats.ITF,
            // 也支持二维码，以防用户扫错类型
            Html5QrcodeSupportedFormats.QR_CODE,
          ],
        };

        // 创建 html5-qrcode 扫描器实例
        this.html5QrcodeScanner = new Html5QrcodeScanner(
          "qr-reader-visit", // DOM元素ID
          config,
          false // 不显示详细日志
        );

        // 开始扫描
        this.html5QrcodeScanner.render(
          this.onScanSuccess.bind(this),
          this.onScanFailure.bind(this)
        );

        this.isScanning = true;
        console.log("[VisitScan] html5-qrcode 扫描器初始化成功");
        return true;
      } catch (error) {
        console.error("[VisitScan] 扫描器初始化失败:", error);
        this.showErrorMessage("扫描器初始化失败，请重试");
        return false;
      } finally {
        this.isProcessing = false;
      }
    },

    // 停止扫描器
    async stopScanner() {
      console.log("[VisitScan] stopScanner called");

      if (this.html5QrcodeScanner) {
        try {
          await this.html5QrcodeScanner.clear();
          this.html5QrcodeScanner = null;
          console.log("[VisitScan] html5-qrcode 扫描器已停止");
        } catch (error) {
          console.error("[VisitScan] 停止扫描器失败:", error);
        }
      }

      this.isScanning = false;
    },

    // 获取摄像头列表
    async getCameraList() {
      try {
        this.availableCameras = await Html5Qrcode.getCameras();
        console.log("[VisitScan] 可用摄像头:", this.availableCameras);

        // 查找后置摄像头
        const backCamera = this.availableCameras.find(
          (camera) =>
            camera.label &&
            (camera.label.toLowerCase().includes("back") ||
              camera.label.toLowerCase().includes("rear") ||
              camera.label.toLowerCase().includes("environment") ||
              camera.label.toLowerCase().includes("后置"))
        );

        if (backCamera) {
          this.currentCameraId = backCamera.id;
          console.log("[VisitScan] 找到后置摄像头:", backCamera.label);
        } else if (this.availableCameras.length > 0) {
          // 如果没有找到明确的后置摄像头，使用第一个
          this.currentCameraId = this.availableCameras[0].id;
          console.log(
            "[VisitScan] 使用默认摄像头:",
            this.availableCameras[0].label
          );
        }
      } catch (error) {
        console.error("[VisitScan] 获取摄像头列表失败:", error);
      }
    },

    // 扫描成功回调
    onScanSuccess(decodedText, decodedResult) {
      console.log("[VisitScan] 扫描成功:", decodedText);
      this.handleScanResult(decodedText);
    },

    // 扫描失败回调
    onScanFailure(error) {
      // 这里不需要处理，html5-qrcode 会持续尝试扫描
    },

    // 切换摄像头
    async toggleCamera() {
      console.log("[VisitScan] toggleCamera called");

      if (!this.availableCameras || this.availableCameras.length <= 1) {
        uni.showToast({
          title: "没有其他可用摄像头",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      try {
        // 找到当前摄像头的索引
        const currentIndex = this.availableCameras.findIndex(
          (camera) => camera.id === this.currentCameraId
        );

        // 切换到下一个摄像头
        const nextIndex = (currentIndex + 1) % this.availableCameras.length;
        const nextCamera = this.availableCameras[nextIndex];

        console.log("[VisitScan] 切换到摄像头:", nextCamera.label);

        // 停止当前扫描器
        await this.stopScanner();

        // 设置新的摄像头ID
        this.currentCameraId = nextCamera.id;

        // 重新初始化扫描器
        await this.initScanner();

        uni.showToast({
          title: `已切换到${nextCamera.label || "摄像头"}`,
          icon: "none",
          duration: 1500,
        });
      } catch (error) {
        console.error("[VisitScan] 切换摄像头失败:", error);
        uni.showToast({
          title: "切换摄像头失败",
          icon: "none",
          duration: 1500,
        });
      }
    },

    // 切换闪光灯
    async toggleFlashlight() {
      console.log("[VisitScan] toggleFlashlight called");

      try {
        // html5-qrcode 库本身不直接支持闪光灯控制
        // 需要通过底层的 MediaStream API 来控制
        if (this.html5QrcodeScanner) {
          // 获取当前的视频流
          const videoElement = document.querySelector("#qr-reader-visit video");
          if (videoElement && videoElement.srcObject) {
            const stream = videoElement.srcObject;
            const videoTracks = stream.getVideoTracks();

            if (videoTracks.length > 0) {
              const track = videoTracks[0];
              const capabilities = track.getCapabilities();

              if (capabilities && capabilities.torch) {
                const settings = track.getSettings();
                const newTorchState = !settings.torch;

                await track.applyConstraints({
                  advanced: [{ torch: newTorchState }],
                });

                this.flashlightOn = newTorchState;
                console.log("[VisitScan] 闪光灯状态:", this.flashlightOn);

                uni.showToast({
                  title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
                  icon: "none",
                  duration: 1000,
                });
              } else {
                uni.showToast({
                  title: "设备不支持闪光灯",
                  icon: "none",
                  duration: 1500,
                });
              }
            }
          }
        }
      } catch (error) {
        console.error("[VisitScan] 切换闪光灯失败:", error);
        uni.showToast({
          title: "闪光灯控制失败",
          icon: "none",
          duration: 1500,
        });
      }
    },

    // 显示错误消息
    showErrorMessage(message) {
      uni.showToast({
        title: message,
        icon: "none",
        duration: 2000,
      });
    },

    // 验证条形码扫描结果
    validateBarcodeResult(text) {
      if (!text || typeof text !== "string" || text.length < 3) {
        return false;
      }

      // 常见条形码格式验证
      // EAN-13: 13位数字
      if (/^\d{13}$/.test(text)) return true;
      // EAN-8: 8位数字
      if (/^\d{8}$/.test(text)) return true;
      // UPC-A: 12位数字
      if (/^\d{12}$/.test(text)) return true;
      // UPC-E: 6-8位数字
      if (/^\d{6,8}$/.test(text)) return true;
      // Code 128/39/93: 字母数字组合
      if (
        /^[A-Z0-9\-\.\$\/\+\%\s]+$/i.test(text) &&
        text.length >= 3 &&
        text.length <= 48
      ) {
        return true;
      }

      // 也支持一些简单的二维码内容（以防用户扫错）
      if (
        text.length > 10 &&
        (/^https?:\/\//.test(text) || text.includes("."))
      ) {
        return true;
      }

      return true; // 放宽验证，让更多内容通过
    },

    // 处理扫描结果
    async handleScanResult(code) {
      console.log("[VisitScan] 扫描到内容:", code, "模式:", this.scanMode);

      // 防止重复处理
      const currentTime = Date.now();
      if (this.isProcessing || currentTime - this.lastScanTime < 2000) {
        return;
      }

      // 验证扫描结果是否为条形码
      if (!this.validateBarcodeResult(code)) {
        uni.showToast({
          title: "请扫描条形码",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      this.lastScanTime = currentTime;
      this.isProcessing = true;

      try {
        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        console.log("[VisitScan] 调用API验证扫码结果:", code);

        // 调用API验证扫码结果
        // 根据扫码类型设置不同的参数名称
        const apiParams = {};
        if (this.scanType === "0") {
          // 巡视扫码 - 台区二维码
          apiParams.tgQrcode = code; // 使用台区二维码专用参数名
          apiParams.type = this.scanType;
        } else {
          // 其他类型扫码使用通用参数名（走访扫码使用客户条形码）
          apiParams.qrcode = code;
          apiParams.type = this.scanType;
        }

        console.log("[VisitScan] API调用参数:", apiParams);

        const response = await getQrcode(apiParams);

        if (response.code === 200) {
          // 扫码成功
          uni.showToast({
            title: "扫码成功",
            icon: "success",
            duration: 1000,
          });

          // 延迟返回上级页面
          setTimeout(() => {
            uni.navigateBack({
              delta: 1,
            });

            // 发送扫码结果事件
            uni.$emit("barcodeScanned", {
              code: code,
              type: "barcode",
              scanType: this.scanType,
              data: response.data,
            });
          }, 1000);
        } else {
          // 验证失败
          uni.showModal({
            title: "验证失败",
            content: response.msg || "扫码验证失败，请重试",
            showCancel: true,
            cancelText: "继续扫描",
            confirmText: "手动输入",
            success: (res) => {
              this.isProcessing = false;
              if (res.confirm) {
                this.showManualInput();
              }
            },
          });
        }
      } catch (error) {
        console.error("[VisitScan] 处理扫码结果失败:", error);
        uni.showModal({
          title: "处理失败",
          content: "网络错误或系统异常，请重试",
          showCancel: true,
          cancelText: "继续扫描",
          confirmText: "手动输入",
          success: (res) => {
            this.isProcessing = false;
            if (res.confirm) {
              this.showManualInput();
            }
          },
        });
      }
    },

    // 验证条形码结果（兼容旧代码）
    validateBarcodeResult(code) {
      return this.isValidScanResult(code);
    },

    // 获取视频元素
    getVideoElement() {
      try {
        // 在 uni-app 中，需要确保 DOM 已经渲染完成
        const video = this.$refs.video;
        console.log("[VisitScan] 获取视频元素引用:", video);

        if (!video) {
          console.error("[VisitScan] 无法获取视频元素引用");
          return null;
        }

        // 在 uni-app 中，需要获取 uni-video 内部的原生 video 元素
        if (video.$el) {
          const uniVideoEl = video.$el;
          console.log("[VisitScan] uni-video 元素:", uniVideoEl);

          // 查找内部的原生 video 元素
          const nativeVideo = uniVideoEl.querySelector("video");
          if (nativeVideo) {
            console.log("[VisitScan] 找到原生 video 元素:", nativeVideo);
            return nativeVideo;
          }

          // 如果没有找到 video 元素，尝试查找其他可能的视频元素
          const videoElements = uniVideoEl.querySelectorAll(
            'video, [tag="video"]'
          );
          if (videoElements.length > 0) {
            console.log("[VisitScan] 找到视频元素:", videoElements[0]);
            return videoElements[0];
          }

          console.log("[VisitScan] 未找到原生 video 元素，返回 uni-video 元素");
          return uniVideoEl;
        }

        return video;
      } catch (error) {
        console.error("[VisitScan] 获取视频元素时发生错误:", error);
        return null;
      }
    },

    // 获取后置摄像头
    getBackCamera() {
      if (!this.devices || this.devices.length === 0) {
        console.log("[VisitScan] 没有可用设备");
        return null;
      }

      console.log(
        "[VisitScan] 开始查找后置摄像头，设备列表:",
        this.devices.map((d) => ({
          deviceId: d.deviceId,
          label: d.label,
        }))
      );

      // 优先级1：查找明确标识为后置摄像头的设备（多语言支持）
      let backCamera = this.devices.find((device) => {
        const label = device.label.toLowerCase();
        return (
          // 英文标签
          label.includes("back") ||
          label.includes("rear") ||
          label.includes("environment") ||
          label.includes("facing back") ||
          label.includes("world") ||
          label.includes("main") ||
          label.includes("primary") ||
          label.includes("wide") ||
          label.includes("camera 0") ||
          label.includes("camera0") ||
          // 中文标签
          label.includes("后置") ||
          label.includes("外置") ||
          label.includes("主摄") ||
          label.includes("主相机") ||
          label.includes("后摄") ||
          label.includes("背面") ||
          label.includes("后面") ||
          // 日文标签
          label.includes("背面") ||
          label.includes("トリプル") ||
          label.includes("デュアル") ||
          label.includes("超広角") ||
          label.includes("広角") ||
          label.includes("望遠") ||
          label.includes("メイン") ||
          label.includes("プライマリ") ||
          // 韩文标签
          label.includes("후면") ||
          label.includes("뒷면") ||
          label.includes("메인") ||
          label.includes("주") ||
          label.includes("기본") ||
          // 品牌特定标签
          // Samsung
          label.includes("triple") ||
          label.includes("dual") ||
          label.includes("ultra wide") ||
          label.includes("telephoto") ||
          label.includes("zoom") ||
          // Huawei/Honor
          label.includes("leica") ||
          label.includes("periscope") ||
          label.includes("超感知") ||
          label.includes("潜望式") ||
          // Xiaomi
          label.includes("小米") ||
          label.includes("redmi") ||
          label.includes("108mp") ||
          label.includes("50mp") ||
          label.includes("48mp") ||
          // Oppo/OnePlus
          label.includes("hasselblad") ||
          label.includes("portrait") ||
          label.includes("macro") ||
          // Vivo
          label.includes("zeiss") ||
          label.includes("gimbal") ||
          // Apple
          label.includes("cinematic") ||
          label.includes("photographic") ||
          // 通用摄像头类型标识
          label.includes("wide angle") ||
          label.includes("ultra-wide") ||
          label.includes("tele") ||
          label.includes("periscope") ||
          label.includes("macro") ||
          label.includes("depth") ||
          label.includes("monochrome") ||
          label.includes("b&w") ||
          label.includes("black and white") ||
          // 数字标识（通常后置摄像头）
          label.includes("camera 1") ||
          label.includes("camera1") ||
          label.includes("cam 1") ||
          label.includes("cam1") ||
          // 通用视频设备标识 - 通常 Video device 1 是后置摄像头
          label.includes("video device 1") ||
          label.includes("video device1") ||
          label.includes("videodevice 1") ||
          label.includes("videodevice1") ||
          // 其他语言
          // 法语
          label.includes("arrière") ||
          label.includes("principal") ||
          // 德语
          label.includes("haupt") ||
          label.includes("rück") ||
          // 西班牙语
          label.includes("trasera") ||
          label.includes("principal") ||
          // 意大利语
          label.includes("posteriore") ||
          label.includes("principale") ||
          // 俄语
          label.includes("основная") ||
          label.includes("задняя") ||
          // 阿拉伯语相关
          label.includes("خلفية") ||
          label.includes("رئيسية") ||
          // 印地语相关
          label.includes("मुख्य") ||
          label.includes("पीछे")
        );
      });

      if (backCamera) {
        console.log("[VisitScan] 找到明确的后置摄像头:", backCamera.label);
        return backCamera;
      }

      // 优先级2：排除明确标识为前置摄像头的设备，选择剩余的第一个
      const frontCameraKeywords = [
        // 英文前置摄像头关键词
        "front",
        "user",
        "facing",
        "selfie",
        "face",
        "inner",
        "secondary",

        // 中文前置摄像头关键词
        "前置",
        "自拍",
        "内置",
        "前摄",
        "前面",
        "人脸",

        // 日文前置摄像头关键词
        "前面",
        "フロント",
        "セルフィー",
        "自撮り",
        "インナー",

        // 韩文前置摄像头关键词
        "전면",
        "앞면",
        "셀피",
        "자촬",
        "사용자",

        // 其他语言前置摄像头关键词
        // 法语
        "avant",
        "utilisateur",

        // 德语
        "vorder",
        "benutzer",

        // 西班牙语
        "frontal",
        "usuario",

        // 意大利语
        "anteriore",
        "utente",

        // 俄语
        "передняя",
        "пользователь",

        // 数字标识（通常前置摄像头）
        "camera 2",
        "camera2",
        "cam 2",
        "cam2",
        // 通用视频设备标识 - 通常 Video device 2 是前置摄像头
        "video device 2",
        "video device2",
        "videodevice 2",
        "videodevice2",
      ];

      const nonFrontCameras = this.devices.filter((device) => {
        const label = device.label.toLowerCase();
        return !frontCameraKeywords.some((keyword) => label.includes(keyword));
      });

      if (nonFrontCameras.length > 0) {
        backCamera = nonFrontCameras[0];
        console.log("[VisitScan] 通过排除法找到后置摄像头:", backCamera.label);
        return backCamera;
      }

      // 优先级3：针对通用"Video device"标签的智能识别
      if (this.devices.length > 1) {
        const videoDevices = this.devices.filter((device) =>
          device.label.toLowerCase().includes("video device")
        );

        if (videoDevices.length >= 2) {
          // 对于通用Video device标签，通常较小的数字是后置摄像头
          const sortedVideoDevices = videoDevices.sort((a, b) => {
            const aNum = parseInt(a.label.match(/\d+/)?.[0] || "0");
            const bNum = parseInt(b.label.match(/\d+/)?.[0] || "0");
            return aNum - bNum;
          });

          backCamera = sortedVideoDevices[0]; // 选择数字最小的
          console.log(
            "[VisitScan] 通过Video device数字排序选择后置摄像头:",
            backCamera.label
          );
          return backCamera;
        }

        // 如果不是Video device标签，按原逻辑选择第二个
        backCamera = this.devices[1];
        console.log("[VisitScan] 通过索引选择后置摄像头:", backCamera.label);
        return backCamera;
      }

      // 最后：返回第一个可用设备
      console.log("[VisitScan] 使用第一个可用设备:", this.devices[0].label);
      return this.devices[0];
    },

    // 切换摄像头
    async toggleCamera() {
      if (!this.devices || this.devices.length <= 1) {
        uni.showToast({
          title: "没有其他可用摄像头",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      try {
        // 停止当前扫描
        if (this.scanControls) {
          try {
            this.scanControls.stop();
            this.scanControls = null;
          } catch (stopError) {
            console.warn("[VisitScan] 停止当前扫描控制器失败:", stopError);
          }
        }

        // 释放当前视频流资源
        if (this.currentStream) {
          try {
            this.currentStream.getTracks().forEach((track) => {
              track.stop();
              console.log("[VisitScan] 已停止视频轨道:", track.label);
            });
            this.currentStream = null;
          } catch (streamError) {
            console.warn("[VisitScan] 释放视频流失败:", streamError);
          }
        }

        // 等待资源完全释放
        await new Promise((resolve) => setTimeout(resolve, 500));

        // 重新获取设备列表，确保设备状态是最新的
        console.log("[VisitScan] 切换摄像头前重新获取设备列表...");
        const videoInputDevices = await this.getVideoDevicesWithRetry();
        this.devices = videoInputDevices;

        console.log(
          "[VisitScan] 当前设备列表:",
          this.devices.map((d) => ({
            deviceId: d.deviceId,
            label: d.label,
          }))
        );

        // 切换到下一个摄像头
        this.cameraIndex = (this.cameraIndex + 1) % this.devices.length;
        const selectedDevice = this.devices[this.cameraIndex];

        console.log("[VisitScan] 切换到摄像头:", {
          device: selectedDevice.label,
          deviceId: selectedDevice.deviceId,
          index: this.cameraIndex,
        });

        // 确保codeReader存在
        if (!this.codeReader) {
          console.log("[VisitScan] codeReader不存在，重新创建");
          this.codeReader = new BrowserMultiFormatReader();

          // 重新配置条形码扫描格式
          const hints = new Map();
          hints.set(DecodeHintType.POSSIBLE_FORMATS, [
            // 条形码格式
            BarcodeFormat.EAN_13,
            BarcodeFormat.EAN_8,
            BarcodeFormat.UPC_A,
            BarcodeFormat.UPC_E,
            BarcodeFormat.CODE_128,
            BarcodeFormat.CODE_39,
            BarcodeFormat.CODE_93,
            BarcodeFormat.CODABAR,
            BarcodeFormat.ITF,
            // 也支持二维码，以防用户扫错类型
            BarcodeFormat.QR_CODE,
            BarcodeFormat.DATA_MATRIX,
          ]);
          hints.set(DecodeHintType.TRY_HARDER, true);
          hints.set(DecodeHintType.ALSO_INVERTED, true);
          this.codeReader.hints = hints;
        }

        // 重新开始扫描 - 添加更好的错误处理
        try {
          this.scanControls = await this.codeReader.decodeFromVideoDevice(
            selectedDevice.deviceId,
            this.videoElement,
            (result, error, controls) => {
              if (result) {
                // 优先验证条形码格式，但也允许二维码
                if (
                  this.isBarcodeFormat(result) ||
                  this.isValidScanResult(result.getText())
                ) {
                  this.handleScanResult(result.getText());
                }
              }
              if (error) {
                if (error.name === "NotFoundException") {
                  // 正常情况，没有找到条形码，继续扫描
                  return;
                } else if (error.name === "ChecksumException") {
                  console.warn("[VisitScan] 条形码校验失败:", error);
                } else if (error.name === "FormatException") {
                  console.warn("[VisitScan] 条形码格式错误:", error);
                } else {
                  console.error("[VisitScan] 扫描错误:", error);
                }
              }
            }
          );
        } catch (scanError) {
          // 如果直接使用设备ID失败，尝试使用约束
          console.warn(
            "[VisitScan] 直接启动扫描失败，尝试使用约束:",
            scanError
          );

          const constraints = {
            video: {
              deviceId: { exact: selectedDevice.deviceId },
              facingMode: { ideal: "environment" },
              width: { ideal: 1280 },
              height: { ideal: 720 },
            },
          };

          this.scanControls = await this.codeReader.decodeFromConstraints(
            constraints,
            this.videoElement,
            (result, error, controls) => {
              if (result) {
                if (
                  this.isBarcodeFormat(result) ||
                  this.isValidScanResult(result.getText())
                ) {
                  this.handleScanResult(result.getText());
                }
              }
              if (error && error.name !== "NotFoundException") {
                console.error("[VisitScan] 约束扫描错误:", error);
              }
            }
          );
        }

        // 获取新的视频流
        if (this.videoElement && this.videoElement.srcObject) {
          this.currentStream = this.videoElement.srcObject;
          console.log("[VisitScan] 已获取新的视频流:", this.currentStream);

          // 检查闪光灯支持
          const videoTracks = this.currentStream.getVideoTracks();
          if (videoTracks.length > 0) {
            const track = videoTracks[0];
            const capabilities = track.getCapabilities();
            console.log("[VisitScan] 摄像头支持的功能:", capabilities);
            if (capabilities && capabilities.torch) {
              console.log("[VisitScan] 设备支持闪光灯功能");
            } else {
              console.log("[VisitScan] 设备不支持闪光灯功能");
            }
          }
        }

        this.isScanning = true;

        uni.showToast({
          title: `已切换到: ${selectedDevice.label}`,
          icon: "none",
          duration: 1500,
        });
      } catch (error) {
        console.error("[VisitScan] 切换摄像头失败:", error);

        // 根据错误类型提供更详细的错误信息
        let errorMessage = "切换摄像头失败";

        if (error instanceof DOMException) {
          switch (error.name) {
            case "NotAllowedError":
              errorMessage = "摄像头权限被拒绝，请检查权限设置";
              break;
            case "NotFoundError":
              errorMessage = "找不到指定的摄像头设备";
              break;
            case "NotReadableError":
              errorMessage = "摄像头正在被其他应用使用";
              break;
            case "OverconstrainedError":
              errorMessage = "摄像头不支持指定的配置";
              break;
            case "SecurityError":
              errorMessage = "安全限制阻止了摄像头访问";
              break;
            default:
              errorMessage = `摄像头访问失败: ${error.message}`;
          }
        }

        uni.showModal({
          title: "切换失败",
          content: errorMessage,
          showCancel: true,
          cancelText: "重试",
          confirmText: "确定",
          success: (res) => {
            if (res.cancel) {
              // 用户选择重试，尝试重新初始化
              setTimeout(() => {
                this.initScanner();
              }, 1000);
            }
          },
        });
      }
    },

    // 控制闪光灯
    async toggleFlashlight() {
      try {
        console.log("[VisitScan] 尝试控制闪光灯，当前状态:", this.flashlightOn);

        // 首先检查当前视频轨道是否支持闪光灯
        if (!this.currentStream) {
          console.log("[VisitScan] 没有可用的视频流");
          uni.showToast({
            title: "摄像头未就绪",
            icon: "none",
            duration: 1500,
          });
          return;
        }

        const videoTracks = this.currentStream.getVideoTracks();
        if (videoTracks.length === 0) {
          console.log("[VisitScan] 没有可用的视频轨道");
          uni.showToast({
            title: "摄像头未就绪",
            icon: "none",
            duration: 1500,
          });
          return;
        }

        const track = videoTracks[0];
        const capabilities = track.getCapabilities();
        console.log("[VisitScan] 当前摄像头能力:", capabilities);

        if (!capabilities || !capabilities.torch) {
          console.log("[VisitScan] 当前设备不支持闪光灯功能");
          uni.showToast({
            title: "当前设备不支持闪光灯",
            icon: "none",
            duration: 1500,
          });
          return;
        }

        // 获取当前设置
        const currentSettings = track.getSettings();
        console.log("[VisitScan] 当前摄像头设置:", currentSettings);

        try {
          // 直接使用 MediaStream API 控制闪光灯
          const newTorchState = !this.flashlightOn;
          console.log("[VisitScan] 尝试设置闪光灯状态为:", newTorchState);

          await track.applyConstraints({
            advanced: [{ torch: newTorchState }],
          });

          // 等待一小段时间确保设置生效
          await new Promise((resolve) => setTimeout(resolve, 100));

          // 验证设置是否真的改变
          const newSettings = track.getSettings();
          console.log("[VisitScan] 新的摄像头设置:", newSettings);

          if (newSettings.torch === newTorchState) {
            this.flashlightOn = newTorchState;
            console.log(
              "[VisitScan] 闪光灯状态已成功更新为:",
              this.flashlightOn
            );
            uni.showToast({
              title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
              icon: "none",
              duration: 1000,
            });
          } else {
            throw new Error("闪光灯状态未能成功更新");
          }
        } catch (error) {
          console.error("[VisitScan] 控制闪光灯失败:", error);

          // 尝试使用备用方法：ZXing
          if (
            this.scanControls &&
            typeof this.scanControls.switchTorch === "function"
          ) {
            console.log("[VisitScan] 尝试使用备用方法: ZXing switchTorch");
            try {
              await this.scanControls.switchTorch();
              // 再次验证设置
              const finalSettings = track.getSettings();
              if (finalSettings.torch !== this.flashlightOn) {
                this.flashlightOn = !this.flashlightOn;
                console.log(
                  "[VisitScan] 使用备用方法成功更新闪光灯状态为:",
                  this.flashlightOn
                );
                uni.showToast({
                  title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
                  icon: "none",
                  duration: 1000,
                });
              } else {
                throw new Error("备用方法也未能更新闪光灯状态");
              }
            } catch (backupError) {
              console.error("[VisitScan] 备用方法也失败:", backupError);
              throw backupError;
            }
          } else {
            throw error;
          }
        }
      } catch (error) {
        console.error("[VisitScan] 所有闪光灯控制方法都失败:", error);
        uni.showToast({
          title: "闪光灯控制失败",
          icon: "none",
          duration: 1500,
        });
      }
    },

    // 停止扫描器
    stopScanner() {
      console.log("[VisitScan] stopScanner called");

      // 先释放视频资源
      this.releaseVideoResources();

      if (this.scanControls) {
        try {
          this.scanControls.stop();
          this.scanControls = null;
          console.log("[VisitScan] 扫描控制器已停止");
        } catch (error) {
          console.error("[VisitScan] 停止扫描控制器失败:", error);
        }
      }

      // 注意：BrowserMultiFormatReader 没有 reset 方法
      // 直接设置为 null 即可
      if (this.codeReader) {
        try {
          // 不调用 reset 方法，直接清空引用
          this.codeReader = null;
          console.log("[VisitScan] codeReader已清空");
        } catch (error) {
          console.error("[VisitScan] 清空codeReader失败:", error);
        }
      }

      this.isScanning = false;
    },

    // 释放视频资源
    releaseVideoResources() {
      console.log("[VisitScan] releaseVideoResources called");

      // 释放当前视频流
      if (this.currentStream) {
        try {
          this.currentStream.getTracks().forEach((track) => {
            console.log("[VisitScan] 停止视频轨道:", track.label, track.kind);
            track.stop();
          });
          this.currentStream = null;
          console.log("[VisitScan] 视频流已释放");
        } catch (error) {
          console.error("[VisitScan] 释放视频流失败:", error);
        }
      }

      // 从视频元素获取并释放视频流
      if (this.videoElement && this.videoElement.srcObject) {
        try {
          const stream = this.videoElement.srcObject;
          if (stream && stream.getTracks) {
            stream.getTracks().forEach((track) => {
              console.log(
                "[VisitScan] 停止视频元素轨道:",
                track.label,
                track.kind
              );
              track.stop();
            });
          }
          this.videoElement.srcObject = null;
          console.log("[VisitScan] 视频元素已清理");
        } catch (error) {
          console.error("[VisitScan] 清理视频元素失败:", error);
        }
      }

      // 强制垃圾回收（如果支持）
      if (window.gc && typeof window.gc === "function") {
        try {
          window.gc();
          console.log("[VisitScan] 已触发垃圾回收");
        } catch (error) {
          console.log("[VisitScan] 垃圾回收不可用");
        }
      }
    },

    // 显示手动输入弹窗
    showManualInput() {
      this.manualCode = "";
      this.isValidManualCode = false;
      this.$refs.inputPopup.open();
    },

    // 关闭手动输入弹窗
    closeManualInput() {
      this.$refs.inputPopup.close();
      this.manualCode = "";
      this.isValidManualCode = false;
    },

    // 手动输入验证
    onManualInput() {
      this.isValidManualCode = this.validateBarcodeResult(this.manualCode);
    },

    // 确认手动输入
    async confirmManualInput() {
      if (!this.isValidManualCode) {
        uni.showToast({
          title: "请输入有效的条形码",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      this.closeManualInput();
      await this.handleScanResult(this.manualCode);
    },

    // 显示扫码帮助
    showScanTips() {
      this.$refs.tipsPopup.open();
    },

    // 关闭扫码帮助
    closeTips() {
      this.$refs.tipsPopup.close();
    },

    // 强制释放所有摄像头资源
    forceReleaseAllCameraResources() {
      console.log("[VisitScan] forceReleaseAllCameraResources called");

      // 强制停止扫描
      this.stopScanner();

      // 释放所有摄像头资源
      this.releaseVideoResources();

      // 重置所有设备状态
      this.devices = [];
      this.cameraIndex = null;
      this.currentStream = null;
      this.videoElement = null;
      console.log("[VisitScan] 已强制释放所有摄像头资源");
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
}

.scan-box {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* html5-qrcode 扫描器样式 */
.qr-reader {
  width: 100%;
  height: 100%;
}

/* 覆盖 html5-qrcode 默认样式 */
:deep(#qr-reader-visit) {
  width: 100% !important;
  height: 100% !important;
}

:deep(#qr-reader-visit video) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

:deep(#qr-reader-visit__dashboard) {
  display: none !important; /* 隐藏默认的控制面板 */
}

:deep(#qr-reader-visit__header_message) {
  display: none !important; /* 隐藏默认的头部消息 */
}

:deep(#qr-reader-visit__camera_selection) {
  display: none !important; /* 隐藏默认的摄像头选择 */
}

:deep(#qr-reader-visit__scan_region) {
  border: none !important; /* 移除默认边框，使用自定义扫描框 */
}

.scan-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  pointer-events: none;
  -webkit-transform: none !important;
  transform: none !important;
}

::v-deep .uni-video-video {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

::v-deep .uni-video-cover,
.uni-video-control {
  display: none !important;
}

.scan-canvas {
  display: none;
}

.scan-line {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 280px;
  height: 180px;
  transform: translate(-50%, -50%);
  border: 1px solid #333;
  box-shadow: 0 0 0 4000px rgba(0, 0, 0, 0.6);
}

.scan-border {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 280px;
  height: 180px;
  transform: translate(-50%, -50%);
  border: 5px solid #138071;
  animation: scanBorder 2s linear infinite;
}

.tip-text {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 100px;
  color: #ffffff;
  text-align: center;
  font-size: 16px;

  .barcode-tip {
    margin-top: 10px;
    font-size: 14px;
    color: #ffcc00;
    font-weight: bold;
    animation: pulse 2s infinite;
  }

  .mode-restriction {
    display: inline-block;
    margin-top: 5px;
    padding: 3px 8px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.success-effect {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
  z-index: 999;
}

.success-icon {
  width: 100px;
  height: 100px;
  background: #138071;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: scaleIn 0.3s ease;
}

.success-text {
  color: #fff;
  font-size: 18px;
  margin-top: 20px;
}

@keyframes scanBorder {
  0% {
    clip-path: inset(0 98% 98% 0);
  }
  25% {
    clip-path: inset(0 0 98% 0);
  }
  50% {
    clip-path: inset(0 0 0 98%);
  }
  75% {
    clip-path: inset(98% 0 0 0);
  }
  100% {
    clip-path: inset(0 98% 98% 0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

:deep(.uni-video-video) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

::v-deep .uni-video-cover,
::v-deep .uni-video-control,
::v-deep .uni-video-poster,
::v-deep .uni-video-button,
::v-deep .uni-video-fullscreen,
::v-deep .uni-video-center-button,
::v-deep .uni-video-seekbar,
::v-deep .uni-video-seekbar-wrap,
::v-deep .uni-video-seekbar-thumb,
::v-deep .uni-video-seekbar-progress,
::v-deep .uni-video-seekbar-buffer,
::v-deep .uni-video-seekbar-played {
  display: none !important;
}

.camera-toggle-btn {
  position: absolute;
  z-index: 1000;
  top: 30px;
  right: 20px;
  padding: 10px 15px;
  background-color: rgba(255, 255, 255, 0.3);
  color: #fff;
  border-radius: 8px;
  text-align: center;
}

.flashlight-btn {
  position: absolute;
  z-index: 1000;
  bottom: 180px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  text {
    margin-top: 5px;
    font-size: 14px;
  }
}

.manual-input-btn {
  position: absolute;
  z-index: 1000;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  padding: 8px 16px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;

  text {
    font-size: 14px;
  }
}

.scan-help-btn {
  position: absolute;
  z-index: 1000;
  bottom: 30px;
  left: 20px;
  padding: 8px 16px;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 6px;

  text {
    font-size: 14px;
  }
}

/* 手动输入弹窗样式 */
.input-popup {
  width: 320px;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.popup-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.popup-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.popup-content {
  padding: 20px;
}

.manual-input {
  width: 100%;
  height: 44px;
  padding: 0 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 16px;
  color: #333;
}

.manual-input:focus {
  border-color: #138071;
}

.input-tip {
  display: block;
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.popup-actions {
  display: flex;
  border-top: 1px solid #f0f0f0;
}

.cancel-btn,
.confirm-btn {
  flex: 1;
  height: 50px;
  border: none;
  font-size: 16px;
  background: #fff;
  color: #666;
}

.cancel-btn {
  border-right: 1px solid #f0f0f0;
}

.confirm-btn {
  color: #138071;
  font-weight: 500;
}

.confirm-btn:disabled {
  color: #ccc;
}

.cancel-btn:active,
.confirm-btn:active {
  background: #f5f5f5;
}

.full-width {
  flex: 1;
  width: 100%;
  border-right: none;
}

/* 帮助弹窗样式 */
.tips-popup {
  width: 320px;
  background: #fff;
  border-radius: 12px;
  overflow: hidden;
}

.tips-content {
  padding: 20px;
}

.tip-item {
  margin-bottom: 16px;
}

.tip-item:last-child {
  margin-bottom: 0;
}

.tip-title {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.tip-desc {
  display: block;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  padding-left: 12px;
}
</style>
