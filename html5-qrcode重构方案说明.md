# 使用 html5-qrcode 重构扫一扫功能方案

## 重构概述

本次重构使用 html5-qrcode 组件库替代了原有的 ZXing 库实现，彻底解决了摄像头默认选择不一致的问题。html5-qrcode 是社区最大、使用人数最多、文档最完善的二维码/条形码扫描库，自带优先使用后置摄像头的参数配置。

## 重构范围

### 1. 重构的文件
- **pages/inspect-scan/index.vue** - 巡视扫码（二维码扫描）
- **pages/visit-scan/index.vue** - 走访扫码（条形码扫描）

### 2. 依赖库变更
- **移除依赖**：`@zxing/library`、`@zxing/browser`
- **使用依赖**：`html5-qrcode`（已存在于 package.json）

## 核心改进

### 1. 摄像头选择优化
```javascript
// html5-qrcode 配置
const config = {
  fps: 10, // 扫描帧率
  qrbox: { width: 250, height: 250 }, // 扫描框大小
  aspectRatio: 1.0, // 宽高比
  // 优先使用后置摄像头的配置
  videoConstraints: {
    facingMode: "environment" // 优先后置摄像头
  }
};
```

### 2. 条形码专门优化（visit-scan）
```javascript
// 条形码扫描专门配置
const config = {
  fps: 10,
  qrbox: { width: 300, height: 150 }, // 条形码扫描框（宽度大于高度）
  aspectRatio: 2.0, // 条形码的宽高比
  videoConstraints: {
    facingMode: "environment"
  },
  // 条形码扫描格式配置
  formatsToSupport: [
    Html5QrcodeSupportedFormats.EAN_13,
    Html5QrcodeSupportedFormats.EAN_8,
    Html5QrcodeSupportedFormats.UPC_A,
    Html5QrcodeSupportedFormats.UPC_E,
    Html5QrcodeSupportedFormats.CODE_128,
    Html5QrcodeSupportedFormats.CODE_39,
    Html5QrcodeSupportedFormats.CODE_93,
    Html5QrcodeSupportedFormats.CODABAR,
    Html5QrcodeSupportedFormats.ITF,
    Html5QrcodeSupportedFormats.QR_CODE // 也支持二维码
  ]
};
```

### 3. 简化的初始化流程
```javascript
// 创建 html5-qrcode 扫描器实例
this.html5QrcodeScanner = new Html5QrcodeScanner(
  "qr-reader", // DOM元素ID
  config,
  false // 不显示详细日志
);

// 开始扫描
this.html5QrcodeScanner.render(
  this.onScanSuccess.bind(this),
  this.onScanFailure.bind(this)
);
```

### 4. 自动摄像头管理
html5-qrcode 库内置了完善的摄像头管理机制：
- 自动检测可用摄像头
- 优先选择后置摄像头
- 自动处理摄像头权限
- 自动资源释放

## 保留的业务逻辑

### 1. API调用逻辑
- 完全保留 `getQrcode` 接口调用
- 保留扫码类型区分（巡视/走访）
- 保留参数名称映射（tgQrcode/qrcode）

### 2. 扫码结果验证
- 保留条形码格式验证逻辑
- 保留扫码结果处理流程
- 保留错误处理和用户提示

### 3. 页面跳转和事件
- 保留 `uni.navigateBack()` 返回逻辑
- 保留 `uni.$emit()` 事件传递
- 保留成功效果显示

### 4. 手动输入功能
- 完全保留手动输入弹窗
- 保留输入验证逻辑
- 保留帮助提示功能

## UI界面保持

### 1. 扫描界面样式
- 保持原有的扫描框样式
- 保持扫描线动画效果
- 保持提示文本显示

### 2. 控制按钮
- 保持切换摄像头按钮
- 保持闪光灯控制按钮
- 保持手动输入按钮（visit-scan）
- 保持扫码帮助按钮（visit-scan）

### 3. 成功效果
- 保持扫码成功动画
- 保持成功提示样式

## 技术优势

### 1. 摄像头兼容性
- **自动后置摄像头选择**：内置算法优先选择后置摄像头
- **设备兼容性更好**：支持更多品牌和型号的手机
- **权限处理更完善**：自动处理摄像头权限请求

### 2. 扫描性能
- **优化的扫描算法**：更快的识别速度
- **更好的条形码支持**：专门优化的条形码识别
- **自适应扫描框**：根据内容类型自动调整

### 3. 资源管理
- **自动资源释放**：无需手动管理摄像头资源
- **内存优化**：更好的内存管理机制
- **错误恢复**：自动处理扫描错误和恢复

### 4. 开发体验
- **简化的API**：更简洁的调用方式
- **完善的文档**：社区支持更好
- **持续更新**：活跃的开源项目

## 样式适配

### 1. html5-qrcode 样式覆盖
```scss
/* 覆盖 html5-qrcode 默认样式 */
:deep(#qr-reader) {
  width: 100% !important;
  height: 100% !important;
}

:deep(#qr-reader video) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

:deep(#qr-reader__dashboard) {
  display: none !important; /* 隐藏默认的控制面板 */
}

:deep(#qr-reader__header_message) {
  display: none !important; /* 隐藏默认的头部消息 */
}

:deep(#qr-reader__camera_selection) {
  display: none !important; /* 隐藏默认的摄像头选择 */
}

:deep(#qr-reader__scan_region) {
  border: none !important; /* 移除默认边框，使用自定义扫描框 */
}
```

## 测试建议

### 1. 功能测试
- 在不同品牌手机上测试摄像头默认选择
- 测试二维码和条形码的识别准确性
- 测试切换摄像头功能
- 测试闪光灯控制功能

### 2. 兼容性测试
- 测试华为/荣耀设备的摄像头选择
- 测试小米、OPPO、vivo等品牌设备
- 测试不同Android版本的兼容性

### 3. 性能测试
- 测试扫描速度和准确性
- 测试内存使用情况
- 测试长时间使用的稳定性

### 4. 业务逻辑测试
- 测试API调用和数据传递
- 测试页面跳转和事件传递
- 测试错误处理和用户提示

## 预期效果

1. **一致的摄像头选择**：所有手机上都会优先选择后置摄像头
2. **更好的扫描体验**：更快的识别速度和更高的准确性
3. **简化的维护成本**：更少的设备兼容性问题
4. **保持业务连续性**：所有业务逻辑和用户体验保持不变

重构完成后，扫一扫功能将具有更好的稳定性和兼容性，同时保持原有的所有功能特性。
