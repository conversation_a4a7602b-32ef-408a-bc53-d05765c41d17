<template>
  <div class="container">
    <div class="scan-box">
      <!-- html5-qrcode 扫描区域 -->
      <div ref="qrReader" id="qr-reader" class="qr-reader"></div>
      <div class="scan-line"></div>
      <div class="tip-text">
        <div>将二维码放入框内进行扫描</div>
        <div class="qrcode-tip">
          <span class="mode-restriction">巡视专用二维码扫描</span>
        </div>
      </div>
    </div>
    <div class="scan-border"></div>
    <div v-if="showSuccessEffect" class="success-effect">
      <div class="success-icon">
        <uni-icons type="checkmarkempty" size="60" color="#fff"></uni-icons>
      </div>
      <div class="success-text">扫码成功</div>
    </div>
    <view class="camera-toggle-btn" @click="toggleCamera">
      <text>切换摄像头</text>
    </view>
    <view class="flashlight-btn" @click="toggleFlashlight">
      <uni-icons
        :type="flashlightOn ? 'fire-filled' : 'fire'"
        size="24"
        color="#fff"
      ></uni-icons>
      <text>{{ flashlightOn ? "关闭闪光灯" : "开启闪光灯" }}</text>
    </view>
  </div>
</template>

<script>
import { Html5QrcodeScanner, Html5Qrcode } from "html5-qrcode";
import { getQrcode } from "@/api";

export default {
  data() {
    return {
      html5QrcodeScanner: null, // html5-qrcode 扫描器实例
      isScanning: false,
      showSuccessEffect: false,
      lastScanTime: 0,
      isProcessing: false,
      isDestroyed: false,
      scanType: "0", // 固定为巡视扫码类型
      flashlightOn: false,
      currentCameraId: null, // 当前使用的摄像头ID
      availableCameras: [], // 可用摄像头列表
      scanMode: "qr", // 固定为二维码模式
    };
  },
  mounted() {
    console.log("[InspectScan] mounted");
    this.isScanning = false;
    this.isProcessing = false;
    this.isDestroyed = false;

    // 延迟初始化，确保DOM完全渲染
    setTimeout(() => {
      this.initScanner();
    }, 500);
  },

  onShow() {
    console.log("[InspectScan] onShow");

    if (this.isDestroyed) {
      this.isDestroyed = false;
      this.isScanning = false;
      this.isProcessing = false;
    }

    // 如果扫描器未运行且未在处理中，重新初始化
    if (!this.isScanning && !this.isDestroyed && !this.isProcessing) {
      console.log("[InspectScan] 重新初始化扫描器");
      setTimeout(() => {
        this.initScanner();
      }, 1000);
    }
  },

  onHide() {
    console.log("[InspectScan] onHide called");
    this.stopScanner();
  },

  onUnload() {
    console.log("[InspectScan] onUnload called");
    this.isDestroyed = true;
    this.stopScanner();
    this.isScanning = false;
    this.isProcessing = false;
  },

  methods: {
    // 初始化扫描器
    async initScanner() {
      console.log("[InspectScan] initScanner called");

      if (this.isScanning || this.isDestroyed || this.isProcessing) {
        console.log(
          "[InspectScan] 扫描器已初始化或组件已销毁或正在处理中，取消初始化"
        );
        return false;
      }

      try {
        this.isProcessing = true;

        // 停止之前的扫描器
        await this.stopScanner();

        // 获取可用摄像头列表
        await this.getCameraList();

        // html5-qrcode 配置
        const config = {
          fps: 10, // 扫描帧率
          qrbox: { width: 250, height: 250 }, // 扫描框大小
          aspectRatio: 1.0, // 宽高比
          // 优先使用后置摄像头的配置
          videoConstraints: {
            facingMode: "environment", // 优先后置摄像头
          },
        };

        // 创建 html5-qrcode 扫描器实例
        this.html5QrcodeScanner = new Html5QrcodeScanner(
          "qr-reader", // DOM元素ID
          config,
          false // 不显示详细日志
        );

        // 开始扫描
        this.html5QrcodeScanner.render(
          this.onScanSuccess.bind(this),
          this.onScanFailure.bind(this)
        );

        this.isScanning = true;
        console.log("[InspectScan] html5-qrcode 扫描器初始化成功");
        return true;
      } catch (error) {
        console.error("[InspectScan] 扫描器初始化失败:", error);
        this.showErrorMessage("扫描器初始化失败，请重试");
        return false;
      } finally {
        this.isProcessing = false;
      }
    },

    // 停止扫描器
    async stopScanner() {
      console.log("[InspectScan] stopScanner called");

      if (this.html5QrcodeScanner) {
        try {
          await this.html5QrcodeScanner.clear();
          this.html5QrcodeScanner = null;
          console.log("[InspectScan] html5-qrcode 扫描器已停止");
        } catch (error) {
          console.error("[InspectScan] 停止扫描器失败:", error);
        }
      }

      this.isScanning = false;
    },

    // 获取摄像头列表
    async getCameraList() {
      try {
        this.availableCameras = await Html5Qrcode.getCameras();
        console.log("[InspectScan] 可用摄像头:", this.availableCameras);

        // 查找后置摄像头
        const backCamera = this.availableCameras.find(
          (camera) =>
            camera.label &&
            (camera.label.toLowerCase().includes("back") ||
              camera.label.toLowerCase().includes("rear") ||
              camera.label.toLowerCase().includes("environment") ||
              camera.label.toLowerCase().includes("后置"))
        );

        if (backCamera) {
          this.currentCameraId = backCamera.id;
          console.log("[InspectScan] 找到后置摄像头:", backCamera.label);
        } else if (this.availableCameras.length > 0) {
          // 如果没有找到明确的后置摄像头，使用第一个
          this.currentCameraId = this.availableCameras[0].id;
          console.log(
            "[InspectScan] 使用默认摄像头:",
            this.availableCameras[0].label
          );
        }
      } catch (error) {
        console.error("[InspectScan] 获取摄像头列表失败:", error);
      }
    },

    // 扫描成功回调
    onScanSuccess(decodedText, decodedResult) {
      console.log("[InspectScan] 扫描成功:", decodedText);
      this.handleScanResult(decodedText);
    },

    // 扫描失败回调
    onScanFailure(error) {
      // 这里不需要处理，html5-qrcode 会持续尝试扫描
    },

    // 切换摄像头
    async toggleCamera() {
      console.log("[InspectScan] toggleCamera called");

      if (!this.availableCameras || this.availableCameras.length <= 1) {
        uni.showToast({
          title: "没有其他可用摄像头",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      try {
        // 找到当前摄像头的索引
        const currentIndex = this.availableCameras.findIndex(
          (camera) => camera.id === this.currentCameraId
        );

        // 切换到下一个摄像头
        const nextIndex = (currentIndex + 1) % this.availableCameras.length;
        const nextCamera = this.availableCameras[nextIndex];

        console.log("[InspectScan] 切换到摄像头:", nextCamera.label);

        // 停止当前扫描器
        await this.stopScanner();

        // 设置新的摄像头ID
        this.currentCameraId = nextCamera.id;

        // 重新初始化扫描器
        await this.initScanner();

        uni.showToast({
          title: `已切换到${nextCamera.label || "摄像头"}`,
          icon: "none",
          duration: 1500,
        });
      } catch (error) {
        console.error("[InspectScan] 切换摄像头失败:", error);
        uni.showToast({
          title: "切换摄像头失败",
          icon: "none",
          duration: 1500,
        });
      }
    },

    // 切换闪光灯
    async toggleFlashlight() {
      console.log("[InspectScan] toggleFlashlight called");

      try {
        // html5-qrcode 库本身不直接支持闪光灯控制
        // 需要通过底层的 MediaStream API 来控制
        if (this.html5QrcodeScanner) {
          // 获取当前的视频流
          const videoElement = document.querySelector("#qr-reader video");
          if (videoElement && videoElement.srcObject) {
            const stream = videoElement.srcObject;
            const videoTracks = stream.getVideoTracks();

            if (videoTracks.length > 0) {
              const track = videoTracks[0];
              const capabilities = track.getCapabilities();

              if (capabilities && capabilities.torch) {
                const settings = track.getSettings();
                const newTorchState = !settings.torch;

                await track.applyConstraints({
                  advanced: [{ torch: newTorchState }],
                });

                this.flashlightOn = newTorchState;
                console.log("[InspectScan] 闪光灯状态:", this.flashlightOn);

                uni.showToast({
                  title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
                  icon: "none",
                  duration: 1000,
                });
              } else {
                uni.showToast({
                  title: "设备不支持闪光灯",
                  icon: "none",
                  duration: 1500,
                });
              }
            }
          }
        }
      } catch (error) {
        console.error("[InspectScan] 切换闪光灯失败:", error);
        uni.showToast({
          title: "闪光灯控制失败",
          icon: "none",
          duration: 1500,
        });
      }
    },

    // 显示错误消息
    showErrorMessage(message) {
      uni.showToast({
        title: message,
        icon: "none",
        duration: 2000,
      });
    },

    // 处理扫描结果
    async handleScanResult(text) {
      console.log("[InspectScan] 扫描到内容:", text, "模式:", this.scanMode);

      // 防止重复处理
      const currentTime = Date.now();
      if (this.isProcessing || currentTime - this.lastScanTime < 2000) {
        return;
      }

      // 验证扫描结果是否为二维码
      if (!this.validateQRCodeResult(text)) {
        uni.showToast({
          title: `请扫描二维码`,
          icon: "none",
          duration: 1500,
        });
        return;
      }

      this.lastScanTime = currentTime;
      this.isProcessing = true;

      try {
        // 显示成功效果
        this.showSuccessEffect = true;
        setTimeout(() => {
          this.showSuccessEffect = false;
        }, 1500);

        console.log("[InspectScan] 对响应数据做点什么");

        // 调用API验证扫码结果
        // 根据扫码类型设置不同的参数名称
        const apiParams = {};
        if (this.scanType === "0") {
          // 巡视扫码 - 台区二维码
          apiParams.tgQrcode = text; // 使用台区二维码专用参数名
          apiParams.type = this.scanType;
        } else {
          // 其他类型扫码使用通用参数名
          apiParams.qrcode = text;
          apiParams.type = this.scanType;
        }

        console.log("[InspectScan] API调用参数:", apiParams);

        const response = await getQrcode(apiParams);

        console.log("[InspectScan] 解密后的数据", response);

        if (response.code === 200) {
          console.log("[InspectScan] 请求成功返回:", response);

          // 扫码成功，跳转到相应页面
          uni.navigateBack({
            delta: 1,
          });

          // 传递扫码结果
          uni.$emit("qrcodeScanned", {
            code: text,
            type: this.scanMode,
            data: response.data,
          });
        } else {
          console.log("[InspectScan] 请求成功返回:", response);

          // 针对"台区二维码不能为空"的特殊错误处理
          let errorMessage = response.msg || "扫码验证失败";

          if (response.msg && response.msg.includes("台区二维码不能为空")) {
            errorMessage = "台区二维码格式错误，请重新扫描正确的台区二维码";
            console.warn("[InspectScan] 台区二维码验证失败:", {
              scanResult: text,
              errorMsg: response.msg,
              errorCode: response.code,
            });
          }

          uni.showModal({
            title: "扫码验证失败",
            content: errorMessage,
            showCancel: true,
            cancelText: "重新扫描",
            confirmText: "确定",
            success: (res) => {
              this.isProcessing = false;
              if (res.cancel) {
                // 用户选择重新扫描，重置处理状态
                console.log("[InspectScan] 用户选择重新扫描");
              }
            },
          });
          return; // 防止下面的代码执行
        }
      } catch (error) {
        console.error("[InspectScan] 处理扫码结果失败:", error);

        // 根据错误类型提供更详细的错误信息
        let errorMessage = "处理扫码结果失败，请重试";

        if (error.message) {
          if (
            error.message.includes("Network") ||
            error.message.includes("网络")
          ) {
            errorMessage = "网络连接失败，请检查网络后重试";
          } else if (
            error.message.includes("timeout") ||
            error.message.includes("超时")
          ) {
            errorMessage = "请求超时，请重新扫描";
          }
        }

        uni.showModal({
          title: "处理失败",
          content: errorMessage,
          showCancel: true,
          cancelText: "重新扫描",
          confirmText: "确定",
          success: (res) => {
            this.isProcessing = false;
            if (res.cancel) {
              console.log("[InspectScan] 用户选择重新扫描");
            }
          },
        });
        return; // 防止下面的代码执行
      }

      // 正常情况下重置处理状态
      this.isProcessing = false;
    },

    // 验证扫描结果是否为二维码
    validateQRCodeResult(text) {
      if (!text || text.length < 1) {
        return false;
      }

      // 二维码验证（相对宽松）
      // URL格式
      if (/^https?:\/\//.test(text)) return true;
      // 包含域名
      if (/\.(com|cn|org|net|gov|edu)/.test(text)) return true;
      // JSON格式
      if (text.startsWith("{") && text.endsWith("}")) {
        try {
          JSON.parse(text);
          return true;
        } catch (e) {
          // 继续其他验证
        }
      }
      // 包含特殊字符或较长内容
      if (text.length > 20 || /[{}:;,\[\]"']/.test(text)) return true;
      // 纯数字但长度超过条形码范围
      if (/^\d+$/.test(text) && text.length > 15) return true;
      // 包含中文字符
      if (/[\u4e00-\u9fa5]/.test(text)) return true;

      return true; // 对于二维码，我们相对宽松一些
    },

    // 获取视频元素
    getVideoElement() {
      try {
        // 在 uni-app 中，需要确保 DOM 已经渲染完成
        const video = this.$refs.video;
        console.log("[InspectScan] 获取视频元素引用:", video);

        if (!video) {
          console.error("[InspectScan] 无法获取视频元素引用");
          return null;
        }

        // 在 uni-app 中，需要获取 uni-video 内部的原生 video 元素
        if (video.$el) {
          const uniVideoEl = video.$el;
          console.log("[InspectScan] uni-video 元素:", uniVideoEl);

          // 查找内部的原生 video 元素
          const nativeVideo = uniVideoEl.querySelector("video");
          if (nativeVideo) {
            console.log("[InspectScan] 找到原生 video 元素:", nativeVideo);
            return nativeVideo;
          }

          // 如果没有找到 video 元素，尝试查找其他可能的视频元素
          const videoElements = uniVideoEl.querySelectorAll(
            'video, [tag="video"]'
          );
          if (videoElements.length > 0) {
            console.log("[InspectScan] 找到视频元素:", videoElements[0]);
            return videoElements[0];
          }

          console.log(
            "[InspectScan] 未找到原生 video 元素，返回 uni-video 元素"
          );
          return uniVideoEl;
        }

        return video;
      } catch (error) {
        console.error("[InspectScan] 获取视频元素时发生错误:", error);
        return null;
      }
    },

    // 获取后置摄像头
    getBackCamera() {
      if (!this.devices || this.devices.length === 0) {
        console.log("[InspectScan] 没有可用设备");
        return null;
      }

      console.log(
        "[InspectScan] 开始查找后置摄像头，设备列表:",
        this.devices.map((d) => ({
          deviceId: d.deviceId,
          label: d.label,
        }))
      );

      // 优先级1：查找明确标识为后置摄像头的设备（多语言支持）
      let backCamera = this.devices.find((device) => {
        const label = device.label.toLowerCase();
        return (
          // 英文标签
          label.includes("back") ||
          label.includes("rear") ||
          label.includes("environment") ||
          label.includes("facing back") ||
          label.includes("world") ||
          label.includes("main") ||
          label.includes("primary") ||
          label.includes("wide") ||
          label.includes("camera 0") ||
          label.includes("camera0") ||
          // 中文标签
          label.includes("后置") ||
          label.includes("外置") ||
          label.includes("主摄") ||
          label.includes("主相机") ||
          label.includes("后摄") ||
          label.includes("背面") ||
          label.includes("后面") ||
          // 日文标签
          label.includes("背面") ||
          label.includes("トリプル") ||
          label.includes("デュアル") ||
          label.includes("超広角") ||
          label.includes("広角") ||
          label.includes("望遠") ||
          label.includes("メイン") ||
          label.includes("プライマリ") ||
          // 韩文标签
          label.includes("후면") ||
          label.includes("뒷면") ||
          label.includes("메인") ||
          label.includes("주") ||
          label.includes("기본") ||
          // 品牌特定标签
          // Samsung
          label.includes("triple") ||
          label.includes("dual") ||
          label.includes("ultra wide") ||
          label.includes("telephoto") ||
          label.includes("zoom") ||
          // Huawei/Honor
          label.includes("leica") ||
          label.includes("periscope") ||
          label.includes("超感知") ||
          label.includes("潜望式") ||
          // Xiaomi
          label.includes("小米") ||
          label.includes("redmi") ||
          label.includes("108mp") ||
          label.includes("50mp") ||
          label.includes("48mp") ||
          // Oppo/OnePlus
          label.includes("hasselblad") ||
          label.includes("portrait") ||
          label.includes("macro") ||
          // Vivo
          label.includes("zeiss") ||
          label.includes("gimbal") ||
          // Apple
          label.includes("cinematic") ||
          label.includes("photographic") ||
          // 通用摄像头类型标识
          label.includes("wide angle") ||
          label.includes("ultra-wide") ||
          label.includes("tele") ||
          label.includes("periscope") ||
          label.includes("macro") ||
          label.includes("depth") ||
          label.includes("monochrome") ||
          label.includes("b&w") ||
          label.includes("black and white") ||
          // 数字标识（通常后置摄像头）
          label.includes("camera 1") ||
          label.includes("camera1") ||
          label.includes("cam 1") ||
          label.includes("cam1") ||
          // 通用视频设备标识 - 通常 Video device 1 是后置摄像头
          label.includes("video device 1") ||
          label.includes("video device1") ||
          label.includes("videodevice 1") ||
          label.includes("videodevice1") ||
          // 其他语言
          // 法语
          label.includes("arrière") ||
          label.includes("principal") ||
          // 德语
          label.includes("haupt") ||
          label.includes("rück") ||
          // 西班牙语
          label.includes("trasera") ||
          label.includes("principal") ||
          // 意大利语
          label.includes("posteriore") ||
          label.includes("principale") ||
          // 俄语
          label.includes("основная") ||
          label.includes("задняя") ||
          // 阿拉伯语相关
          label.includes("خلفية") ||
          label.includes("رئيسية") ||
          // 印地语相关
          label.includes("मुख्य") ||
          label.includes("पीछे")
        );
      });

      if (backCamera) {
        console.log("[InspectScan] 找到明确的后置摄像头:", backCamera.label);
        return backCamera;
      }

      // 优先级2：排除明确标识为前置摄像头的设备，选择剩余的第一个
      const frontCameraKeywords = [
        // 英文前置摄像头关键词
        "front",
        "user",
        "facing",
        "selfie",
        "face",
        "inner",
        "secondary",

        // 中文前置摄像头关键词
        "前置",
        "自拍",
        "内置",
        "前摄",
        "前面",
        "人脸",

        // 日文前置摄像头关键词
        "前面",
        "フロント",
        "セルフィー",
        "自撮り",
        "インナー",

        // 韩文前置摄像头关键词
        "전면",
        "앞면",
        "셀피",
        "자촬",
        "사용자",

        // 其他语言前置摄像头关键词
        // 法语
        "avant",
        "utilisateur",

        // 德语
        "vorder",
        "benutzer",

        // 西班牙语
        "frontal",
        "usuario",

        // 意大利语
        "anteriore",
        "utente",

        // 俄语
        "передняя",
        "пользователь",

        // 数字标识（通常前置摄像头）
        "camera 2",
        "camera2",
        "cam 2",
        "cam2",
        // 通用视频设备标识 - 通常 Video device 2 是前置摄像头
        "video device 2",
        "video device2",
        "videodevice 2",
        "videodevice2",
      ];

      const nonFrontCameras = this.devices.filter((device) => {
        const label = device.label.toLowerCase();
        return !frontCameraKeywords.some((keyword) => label.includes(keyword));
      });

      if (nonFrontCameras.length > 0) {
        backCamera = nonFrontCameras[0];
        console.log(
          "[InspectScan] 通过排除法找到后置摄像头:",
          backCamera.label
        );
        return backCamera;
      }

      // 优先级3：针对通用"Video device"标签的智能识别
      if (this.devices.length > 1) {
        const videoDevices = this.devices.filter((device) =>
          device.label.toLowerCase().includes("video device")
        );

        if (videoDevices.length >= 2) {
          // 对于通用Video device标签，通常较小的数字是后置摄像头
          const sortedVideoDevices = videoDevices.sort((a, b) => {
            const aNum = parseInt(a.label.match(/\d+/)?.[0] || "0");
            const bNum = parseInt(b.label.match(/\d+/)?.[0] || "0");
            return aNum - bNum;
          });

          backCamera = sortedVideoDevices[0]; // 选择数字最小的
          console.log(
            "[InspectScan] 通过Video device数字排序选择后置摄像头:",
            backCamera.label
          );
          return backCamera;
        }

        // 如果不是Video device标签，按原逻辑选择第二个
        backCamera = this.devices[1];
        console.log("[InspectScan] 通过索引选择后置摄像头:", backCamera.label);
        return backCamera;
      }

      // 最后：返回第一个可用设备
      console.log("[InspectScan] 使用第一个可用设备:", this.devices[0].label);
      return this.devices[0];
    },

    // 切换摄像头
    async toggleCamera() {
      if (!this.devices || this.devices.length <= 1) {
        uni.showToast({
          title: "没有其他可用摄像头",
          icon: "none",
          duration: 1500,
        });
        return;
      }

      try {
        console.log("[InspectScan] 开始切换摄像头...");

        // 停止当前扫描
        if (this.scanControls) {
          try {
            this.scanControls.stop();
            this.scanControls = null;
            console.log("[InspectScan] 已停止扫描控制器");
          } catch (stopError) {
            console.warn("[InspectScan] 停止当前扫描控制器失败:", stopError);
          }
        }

        // 彻底释放当前视频流资源
        if (this.currentStream) {
          try {
            this.currentStream.getTracks().forEach((track) => {
              track.stop();
              console.log("[InspectScan] 已停止视频轨道:", track.label);
            });
            this.currentStream = null;
          } catch (streamError) {
            console.warn("[InspectScan] 释放视频流失败:", streamError);
          }
        }

        // 清理视频元素
        if (this.videoElement && this.videoElement.srcObject) {
          try {
            const stream = this.videoElement.srcObject;
            if (stream && stream.getTracks) {
              stream.getTracks().forEach((track) => {
                track.stop();
                console.log("[InspectScan] 已停止视频元素轨道:", track.label);
              });
            }
            this.videoElement.srcObject = null;
            this.videoElement.pause();
            console.log("[InspectScan] 已清理视频元素");
          } catch (cleanError) {
            console.warn("[InspectScan] 清理视频元素失败:", cleanError);
          }
        }

        // 等待资源完全释放（增加等待时间）
        console.log("[InspectScan] 等待资源释放...");
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // 重新获取设备列表，确保设备状态是最新的
        console.log("[InspectScan] 切换摄像头前重新获取设备列表...");
        const videoInputDevices = await this.getVideoDevicesWithRetry();
        this.devices = videoInputDevices;

        console.log(
          "[InspectScan] 当前设备列表:",
          this.devices.map((d) => ({
            deviceId: d.deviceId,
            label: d.label,
          }))
        );

        // 切换到下一个摄像头
        this.cameraIndex = (this.cameraIndex + 1) % this.devices.length;
        const selectedDevice = this.devices[this.cameraIndex];

        console.log("[InspectScan] 切换到摄像头:", {
          device: selectedDevice.label,
          deviceId: selectedDevice.deviceId,
          index: this.cameraIndex,
        });

        // 确保codeReader存在
        if (!this.codeReader) {
          console.log("[InspectScan] codeReader不存在，重新创建");
          this.codeReader = new BrowserMultiFormatReader();

          // 重新设置二维码模式
          const hints = new Map();
          hints.set(DecodeHintType.POSSIBLE_FORMATS, [
            BarcodeFormat.QR_CODE,
            BarcodeFormat.DATA_MATRIX,
            BarcodeFormat.AZTEC,
            BarcodeFormat.PDF_417,
          ]);
          hints.set(DecodeHintType.ALSO_INVERTED, true);
          hints.set(DecodeHintType.TRY_HARDER, true);
          this.codeReader.hints = hints;
        }

        // 重新开始扫描 - 优先使用约束条件
        console.log("[InspectScan] 使用约束条件启动扫描...");
        const constraints = {
          video: {
            deviceId: { exact: selectedDevice.deviceId },
            facingMode: { ideal: "environment" },
            width: { ideal: 1280 },
            height: { ideal: 720 },
          },
        };

        try {
          this.scanControls = await this.codeReader.decodeFromConstraints(
            constraints,
            this.videoElement,
            (result, error, controls) => {
              if (result) {
                this.handleScanResult(result.getText());
              }
              if (error && !(error instanceof NotFoundException)) {
                console.error("[InspectScan] 约束扫描错误:", error);
              }
            }
          );
          console.log("[InspectScan] 约束条件启动成功");
        } catch (constraintError) {
          // 如果约束条件失败，尝试直接使用设备ID
          console.warn(
            "[InspectScan] 约束条件启动失败，尝试直接使用设备ID:",
            constraintError
          );

          this.scanControls = await this.codeReader.decodeFromVideoDevice(
            selectedDevice.deviceId,
            this.videoElement,
            (result, error, controls) => {
              if (result) {
                this.handleScanResult(result.getText());
              }
              if (error && !(error instanceof NotFoundException)) {
                console.error("[InspectScan] 设备ID扫描错误:", error);
              }
            }
          );
          console.log("[InspectScan] 设备ID启动成功");
        }

        // 获取新的视频流
        if (this.videoElement && this.videoElement.srcObject) {
          this.currentStream = this.videoElement.srcObject;
          console.log("[InspectScan] 已获取新的视频流:", this.currentStream);
        }

        this.isScanning = true;

        uni.showToast({
          title: `已切换到: ${selectedDevice.label}`,
          icon: "none",
          duration: 1500,
        });
      } catch (error) {
        console.error("[InspectScan] 切换摄像头失败:", error);

        // 根据错误类型提供更详细的错误信息
        let errorMessage = "切换摄像头失败";

        if (error instanceof DOMException) {
          switch (error.name) {
            case "NotAllowedError":
              errorMessage = "摄像头权限被拒绝，请检查权限设置";
              break;
            case "NotFoundError":
              errorMessage = "找不到指定的摄像头设备";
              break;
            case "NotReadableError":
              errorMessage = "摄像头正在被其他应用使用，请稍后重试";
              break;
            case "OverconstrainedError":
              errorMessage = "摄像头不支持指定的配置";
              break;
            case "SecurityError":
              errorMessage = "安全限制阻止了摄像头访问";
              break;
            case "AbortError":
              errorMessage = "摄像头操作被中断";
              break;
            case "TypeError":
              errorMessage = "摄像头配置错误";
              break;
            default:
              errorMessage = `摄像头访问失败: ${error.message}`;
              break;
          }
        }

        uni.showToast({
          title: errorMessage,
          icon: "none",
          duration: 2000,
        });

        // 尝试重新初始化（延长等待时间）
        setTimeout(() => {
          console.log("[InspectScan] 切换失败后尝试重新初始化...");
          this.initScanner();
        }, 2000);
      }
    },

    // 控制闪光灯
    async toggleFlashlight() {
      try {
        console.log(
          "[InspectScan] 尝试控制闪光灯，当前状态:",
          this.flashlightOn
        );
        console.log("[InspectScan] scanControls:", this.scanControls);

        // 方法1：尝试使用ZXing的switchTorch方法
        if (
          this.scanControls &&
          typeof this.scanControls.switchTorch === "function"
        ) {
          try {
            await this.scanControls.switchTorch();
            this.flashlightOn = !this.flashlightOn;
            console.log("[InspectScan] 使用ZXing switchTorch成功");

            uni.showToast({
              title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
              icon: "none",
              duration: 1000,
            });
            return;
          } catch (error) {
            console.warn("[InspectScan] ZXing switchTorch失败:", error);
          }
        }

        // 方法2：尝试使用MediaStream API控制闪光灯
        if (this.currentStream) {
          try {
            const videoTracks = this.currentStream.getVideoTracks();
            console.log("[InspectScan] 视频轨道数量:", videoTracks.length);

            if (videoTracks.length > 0) {
              const track = videoTracks[0];
              const capabilities = track.getCapabilities();
              console.log("[InspectScan] 摄像头能力:", capabilities);

              if (capabilities && capabilities.torch) {
                await track.applyConstraints({
                  advanced: [{ torch: !this.flashlightOn }],
                });
                this.flashlightOn = !this.flashlightOn;
                console.log("[InspectScan] 使用MediaStream API控制闪光灯成功");

                uni.showToast({
                  title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
                  icon: "none",
                  duration: 1000,
                });
                return;
              } else {
                console.log("[InspectScan] 设备不支持torch功能");
              }
            }
          } catch (error) {
            console.warn("[InspectScan] MediaStream API控制闪光灯失败:", error);
          }
        }

        // 方法3：尝试从视频元素获取流并控制闪光灯
        if (this.videoElement && this.videoElement.srcObject) {
          try {
            const stream = this.videoElement.srcObject;
            const videoTracks = stream.getVideoTracks();
            console.log(
              "[InspectScan] 从视频元素获取的轨道数量:",
              videoTracks.length
            );

            if (videoTracks.length > 0) {
              const track = videoTracks[0];
              const capabilities = track.getCapabilities();
              console.log("[InspectScan] 视频元素轨道能力:", capabilities);

              if (capabilities && capabilities.torch) {
                await track.applyConstraints({
                  advanced: [{ torch: !this.flashlightOn }],
                });
                this.flashlightOn = !this.flashlightOn;
                console.log("[InspectScan] 使用视频元素流控制闪光灯成功");

                uni.showToast({
                  title: this.flashlightOn ? "闪光灯已开启" : "闪光灯已关闭",
                  icon: "none",
                  duration: 1000,
                });
                return;
              }
            }
          } catch (error) {
            console.warn("[InspectScan] 视频元素流控制闪光灯失败:", error);
          }
        }

        // 所有方法都失败
        console.log("[InspectScan] 所有闪光灯控制方法都失败");
        uni.showToast({
          title: "当前设备不支持闪光灯",
          icon: "none",
          duration: 1500,
        });
      } catch (error) {
        console.error("[InspectScan] 控制闪光灯失败:", error);
        uni.showToast({
          title: "闪光灯控制失败",
          icon: "none",
          duration: 1500,
        });
      }
    },

    // 停止扫描器
    stopScanner() {
      console.log("[InspectScan] stopScanner called");

      // 先释放视频资源
      this.releaseVideoResources();

      if (this.scanControls) {
        try {
          this.scanControls.stop();
          this.scanControls = null;
          console.log("[InspectScan] 扫描控制器已停止");
        } catch (error) {
          console.error("[InspectScan] 停止扫描控制器失败:", error);
        }
      }

      if (this.codeReader) {
        try {
          // BrowserMultiFormatReader 没有 reset 方法，直接设置为 null
          this.codeReader = null;
          console.log("[InspectScan] codeReader已重置");
        } catch (error) {
          console.error("[InspectScan] 重置codeReader失败:", error);
        }
      }

      this.isScanning = false;
    },

    // 释放视频资源
    releaseVideoResources() {
      console.log("[InspectScan] releaseVideoResources called");

      // 释放当前视频流
      if (this.currentStream) {
        try {
          this.currentStream.getTracks().forEach((track) => {
            console.log("[InspectScan] 停止视频轨道:", track.label, track.kind);
            track.stop();
          });
          this.currentStream = null;
          console.log("[InspectScan] 视频流已释放");
        } catch (error) {
          console.error("[InspectScan] 释放视频流失败:", error);
        }
      }

      // 从视频元素获取并释放视频流
      if (this.videoElement && this.videoElement.srcObject) {
        try {
          const stream = this.videoElement.srcObject;
          if (stream && stream.getTracks) {
            stream.getTracks().forEach((track) => {
              console.log(
                "[InspectScan] 停止视频元素轨道:",
                track.label,
                track.kind
              );
              track.stop();
            });
          }
          this.videoElement.srcObject = null;
          console.log("[InspectScan] 视频元素已清理");
        } catch (error) {
          console.error("[InspectScan] 清理视频元素失败:", error);
        }
      }

      // 清理video元素，确保完全释放
      if (this.videoElement) {
        try {
          this.videoElement.pause();
          this.videoElement.currentTime = 0;
          console.log("[InspectScan] 视频元素状态已重置");
        } catch (error) {
          console.warn("[InspectScan] 重置视频元素状态失败:", error);
        }
      }

      // 强制垃圾回收（如果支持）
      if (window.gc && typeof window.gc === "function") {
        try {
          window.gc();
          console.log("[InspectScan] 已触发垃圾回收");
        } catch (error) {
          console.log("[InspectScan] 垃圾回收不可用");
        }
      }
    },

    // 等待摄像头资源释放
    async waitForCameraRelease() {
      console.log("[InspectScan] 等待摄像头资源释放...");

      // 检查是否有活跃的媒体流
      let retryCount = 0;
      const maxRetries = 5;

      while (retryCount < maxRetries) {
        try {
          // 尝试获取设备列表来检测摄像头状态
          const devices = await navigator.mediaDevices.enumerateDevices();
          const videoDevices = devices.filter(
            (device) => device.kind === "videoinput"
          );

          console.log(
            "[InspectScan] 检测到摄像头设备数量:",
            videoDevices.length
          );

          // 如果能正常获取设备列表，说明摄像头资源可用
          if (videoDevices.length > 0) {
            console.log("[InspectScan] 摄像头资源检测正常");
            break;
          }
        } catch (error) {
          console.warn("[InspectScan] 摄像头资源检测失败:", error);
        }

        retryCount++;
        console.log(
          `[InspectScan] 等待摄像头资源释放，重试 ${retryCount}/${maxRetries}`
        );
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    },

    // 带重试的获取视频设备列表
    async getVideoDevicesWithRetry() {
      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          const devices = await BrowserCodeReader.listVideoInputDevices();
          if (devices && devices.length > 0) {
            console.log("[InspectScan] 成功获取视频设备列表");
            return devices;
          }
        } catch (error) {
          console.warn(
            `[InspectScan] 获取视频设备失败，重试 ${
              retryCount + 1
            }/${maxRetries}:`,
            error
          );
        }

        retryCount++;
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }

      throw new Error("无法获取摄像头设备列表");
    },

    // 强制释放可能残留的摄像头资源
    forceReleaseAllCameraResources() {
      console.log("[InspectScan] forceReleaseAllCameraResources called");

      // 强制停止扫描和释放资源
      this.forceStopAndRelease();

      // 清理所有摄像头资源
      this.devices = [];
      this.cameraIndex = null;
      this.currentStream = null;
      this.videoElement = null;
      console.log("[InspectScan] 已清理所有摄像头资源");
    },

    // 确保摄像头资源可用
    async ensureCameraResourcesAvailable() {
      console.log("[InspectScan] 检查摄像头资源可用性...");

      let retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          // 尝试获取媒体设备权限和列表
          const devices = await navigator.mediaDevices.enumerateDevices();
          const videoDevices = devices.filter(
            (device) => device.kind === "videoinput"
          );

          if (videoDevices.length === 0) {
            throw new Error("没有找到视频输入设备");
          }

          // 尝试获取一个临时的媒体流来测试摄像头是否可用
          const testConstraints = {
            video: {
              facingMode: { ideal: "environment" },
            },
          };

          try {
            const testStream = await navigator.mediaDevices.getUserMedia(
              testConstraints
            );
            // 立即停止测试流
            testStream.getTracks().forEach((track) => track.stop());
            console.log("[InspectScan] 摄像头资源检查通过");
            return true;
          } catch (mediaError) {
            if (mediaError.name === "NotReadableError") {
              throw new Error("摄像头正在被其他应用使用");
            } else if (mediaError.name === "NotAllowedError") {
              throw new Error("摄像头权限被拒绝");
            } else {
              throw mediaError;
            }
          }
        } catch (error) {
          console.warn(
            `[InspectScan] 摄像头资源检查失败，重试 ${
              retryCount + 1
            }/${maxRetries}:`,
            error
          );

          if (retryCount === maxRetries - 1) {
            // 最后一次重试失败，抛出错误
            throw new Error(`摄像头不可用: ${error.message}`);
          }

          retryCount++;
          // 等待一段时间后重试
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100vw;
  height: 100vh;
  background-color: #000;
  overflow: hidden;
}

.scan-box {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* html5-qrcode 扫描器样式 */
.qr-reader {
  width: 100%;
  height: 100%;
}

/* 覆盖 html5-qrcode 默认样式 */
:deep(#qr-reader) {
  width: 100% !important;
  height: 100% !important;
}

:deep(#qr-reader video) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

:deep(#qr-reader__dashboard) {
  display: none !important; /* 隐藏默认的控制面板 */
}

:deep(#qr-reader__header_message) {
  display: none !important; /* 隐藏默认的头部消息 */
}

:deep(#qr-reader__camera_selection) {
  display: none !important; /* 隐藏默认的摄像头选择 */
}

:deep(#qr-reader__scan_region) {
  border: none !important; /* 移除默认边框，使用自定义扫描框 */
}

.scan-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  pointer-events: none;
  -webkit-transform: none !important;
  transform: none !important;
}

::v-deep .uni-video-video {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

::v-deep .uni-video-cover,
.uni-video-control {
  display: none !important;
}

.scan-canvas {
  display: none;
}

.scan-line {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 240px;
  height: 240px;
  transform: translate(-50%, -50%);
  border: 1px solid #333;
  box-shadow: 0 0 0 4000px rgba(0, 0, 0, 0.6);
}

.scan-border {
  position: absolute;
  left: 50%;
  top: 50%;
  width: 240px;
  height: 240px;
  transform: translate(-50%, -50%);
  border: 5px solid #138071;
  animation: scanBorder 2s linear infinite;
}

.tip-text {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 100px;
  color: #ffffff;
  text-align: center;
  font-size: 16px;

  .qrcode-tip {
    margin-top: 10px;
    font-size: 14px;
    color: #00ccff;
    font-weight: bold;
    animation: pulse 2s infinite;
  }

  .mode-restriction {
    display: inline-block;
    margin-top: 5px;
    padding: 3px 8px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.success-effect {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease;
  z-index: 999;
}

.success-icon {
  width: 100px;
  height: 100px;
  background: #138071;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: scaleIn 0.3s ease;
}

.success-text {
  color: #fff;
  font-size: 18px;
  margin-top: 20px;
}

@keyframes scanBorder {
  0% {
    clip-path: inset(0 98% 98% 0);
  }
  25% {
    clip-path: inset(0 0 98% 0);
  }
  50% {
    clip-path: inset(0 0 0 98%);
  }
  75% {
    clip-path: inset(98% 0 0 0);
  }
  100% {
    clip-path: inset(0 98% 98% 0);
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

:deep(.uni-video-video) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

::v-deep .uni-video-cover,
::v-deep .uni-video-control,
::v-deep .uni-video-poster,
::v-deep .uni-video-button,
::v-deep .uni-video-fullscreen,
::v-deep .uni-video-center-button,
::v-deep .uni-video-seekbar,
::v-deep .uni-video-seekbar-wrap,
::v-deep .uni-video-seekbar-thumb,
::v-deep .uni-video-seekbar-progress,
::v-deep .uni-video-seekbar-buffer,
::v-deep .uni-video-seekbar-played {
  display: none !important;
}

.camera-toggle-btn {
  position: absolute;
  z-index: 1000;
  top: 30px;
  right: 20px;
  padding: 10px 15px;
  background-color: rgba(255, 255, 255, 0.3);
  color: #fff;
  border-radius: 8px;
  text-align: center;
}

.flashlight-btn {
  position: absolute;
  z-index: 1000;
  bottom: 180px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  border-radius: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  text {
    margin-top: 5px;
    font-size: 14px;
  }
}
</style>
